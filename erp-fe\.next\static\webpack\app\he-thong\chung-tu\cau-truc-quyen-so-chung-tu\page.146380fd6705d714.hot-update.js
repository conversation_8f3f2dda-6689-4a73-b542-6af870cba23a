"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx":
/*!************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx ***!
  \************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInputTableColumns: function() { return /* binding */ getInputTableColumns; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/search-columns */ \"(app-pages-browser)/./src/constants/search-columns/index.tsx\");\n/* harmony import */ var _components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table/components */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/components/index.ts\");\n/* harmony import */ var _components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom/arito/form/custom-search-field */ \"(app-pages-browser)/./src/components/custom/arito/form/custom-search-field/index.tsx\");\n/* harmony import */ var _constants_query_keys__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/constants/query-keys */ \"(app-pages-browser)/./src/constants/query-keys.ts\");\n\n\n\n\n\n\nconst getInputTableColumns = (param)=>{\n    let { onCellValueChange, onChungTuSelect, onUserSelect } = param;\n    return [\n        {\n            field: \"ma_ct\",\n            headerName: \"M\\xe3 chứng từ\",\n            width: 175,\n            renderCell: (params)=>{\n                var _params_row_ma_ct_data;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: ((_params_row_ma_ct_data = params.row.ma_ct_data) === null || _params_row_ma_ct_data === void 0 ? void 0 : _params_row_ma_ct_data.ma_ct) || params.row.ma_ct || \"\",\n                    searchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].CHUNG_TU),\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.chungTuSearchColumns,\n                    dialogTitle: \"Danh mục chứng từ\",\n                    className: \"w-full\",\n                    columnDisplay: \"ma_ct\",\n                    onRowSelection: (selectedChungTu)=>{\n                        if (selectedChungTu) {\n                            onCellValueChange(params.row.uuid, \"ma_ct_data\", selectedChungTu || \"\");\n                            if (onChungTuSelect) {\n                                onChungTuSelect(params.row.uuid, selectedChungTu);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"ten_ct\",\n            headerName: \"T\\xean chứng từ\",\n            width: 200,\n            renderCell: (params)=>{\n                var _params_row_ma_ct_data;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"ten_ct\",\n                    type: \"text\",\n                    value: ((_params_row_ma_ct_data = params.row.ma_ct_data) === null || _params_row_ma_ct_data === void 0 ? void 0 : _params_row_ma_ct_data.ten_ct) || params.row.ten_ct || \"\",\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 7\n                }, undefined);\n            }\n        },\n        {\n            field: \"username\",\n            headerName: \"Người sử dụng Nh\\xf3m\",\n            width: 175,\n            renderCell: (params)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_custom_search_field__WEBPACK_IMPORTED_MODULE_4__.SearchField, {\n                    value: params.row.username || \"\",\n                    onValueChange: (newValue)=>{\n                        onCellValueChange(params.row.uuid, \"username\", newValue);\n                        // Khi người dùng xóa giá trị, cũng xóa related field\n                        if (!newValue) {\n                            onCellValueChange(params.row.uuid, \"first_name\", \"\");\n                        }\n                    },\n                    otherSearchEndpoint: \"/\".concat(_constants_query_keys__WEBPACK_IMPORTED_MODULE_5__[\"default\"].NGUOI_SU_DUNG),\n                    searchEndpoint: \"\",\n                    searchColumns: _constants_search_columns__WEBPACK_IMPORTED_MODULE_2__.userSearchColumns,\n                    dialogTitle: \"Danh mục người sử dụng\",\n                    className: \"w-full\",\n                    displayRelatedField: \"first_name\",\n                    columnDisplay: \"username\",\n                    relatedFieldValue: params.row.first_name || \"\",\n                    onRowSelection: (selectedUser)=>{\n                        if (selectedUser) {\n                            onCellValueChange(params.row.uuid, \"username\", selectedUser.username);\n                            onCellValueChange(params.row.uuid, \"first_name\", selectedUser.first_name);\n                            // Call parent callback for additional processing\n                            if (onUserSelect) {\n                                onUserSelect(params.row.uuid, selectedUser);\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined);\n            }\n        },\n        {\n            field: \"first_name\",\n            headerName: \"T\\xean người sử dụng\",\n            width: 200,\n            renderCell: (params)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table_components__WEBPACK_IMPORTED_MODULE_3__.CellField, {\n                    name: \"first_name\",\n                    type: \"text\",\n                    value: params.row.first_name,\n                    onValueChange: (newValue)=>onCellValueChange(params.row.uuid, \"first_name\", newValue),\n                    disabled: true\n                }, void 0, false, {\n                    fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\cols.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 7\n                }, undefined)\n        }\n    ];\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\n"));

/***/ })

});