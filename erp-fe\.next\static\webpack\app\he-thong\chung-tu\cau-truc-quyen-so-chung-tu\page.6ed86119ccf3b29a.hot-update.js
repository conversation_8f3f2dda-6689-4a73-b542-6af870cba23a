"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx":
/*!*************************************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx ***!
  \*************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_custom_input_table__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/custom-input-table */ \"(app-pages-browser)/./src/components/custom/arito/custom-input-table/index.tsx\");\n/* harmony import */ var _InputTableActionBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputTableActionBar */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/InputTableActionBar.tsx\");\n/* harmony import */ var _cols__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cols */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/cols.tsx\");\n\n\n\n\n\nconst InputTableTab = (param)=>{\n    let { mode, rows, selectedRowUuid, onRowClick, onAddRow, onDeleteRow, onCopyRow, onPasteRow, onMoveRow, onCellValueChange, onChungTuSelect, onUserSelect } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_custom_input_table__WEBPACK_IMPORTED_MODULE_2__.InputTable, {\n        rows: rows,\n        onRowClick: onRowClick,\n        selectedRowId: selectedRowUuid || undefined,\n        columns: (0,_cols__WEBPACK_IMPORTED_MODULE_4__.getInputTableColumns)({\n            onCellValueChange,\n            onChungTuSelect,\n            onUserSelect\n        }),\n        getRowId: (row)=>(row === null || row === void 0 ? void 0 : row.uuid) || \"\",\n        actionButtons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputTableActionBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            mode: mode,\n            handleAddRow: onAddRow,\n            handleDeleteRow: onDeleteRow,\n            handleCopyRow: onCopyRow,\n            handlePasteRow: onPasteRow,\n            handleMoveRow: onMoveRow\n        }, void 0, false, void 0, void 0)\n    }, void 0, false, {\n        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\InputTableTab\\\\index.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n_c = InputTableTab;\n/* harmony default export */ __webpack_exports__[\"default\"] = (InputTableTab);\nvar _c;\n$RefreshReg$(_c, \"InputTableTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx\n"));

/***/ })

});