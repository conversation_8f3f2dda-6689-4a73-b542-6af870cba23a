import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import { chungTuSearchColumns, userSearchColumns } from '@/constants/search-columns';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import QUERY_KEYS from '@/constants/query-keys';
import type { ChungTu } from '@/types/schemas';

// Interface for User data (matching backend structure with profile)
interface UserProfile {
  uuid: string;
  nickname?: string | null;
}

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name?: string;
  is_staff?: boolean;
  is_active?: boolean;
  date_joined?: string;
  profile: UserProfile;
}

// Props interface following GeneralTab pattern
interface InputTableColumnsProps {
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  onChungTuSelect?: (rowUuid: string, chungTu: ChungTu) => void;
  onUserSelect?: (rowUuid: string, user: User) => void;
}

export const getInputTableColumns = ({
  onCellValueChange,
  onChungTuSelect,
  onUserSelect
}: InputTableColumnsProps): GridColDef[] => [
  {
    field: 'ma_ct',
    headerName: 'Mã chứng từ',
    width: 175,
    renderCell: params => {
      return (
        <SearchField<ChungTu>
          value={params.row.ma_ct_data?.ma_ct || params.row.ma_ct || ''}
          searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
          searchColumns={chungTuSearchColumns}
          dialogTitle='Danh mục chứng từ'
          className='w-full'
          columnDisplay='ma_ct'
          onRowSelection={(selectedChungTu: ChungTu) => {
            if (selectedChungTu) {
              onCellValueChange(params.row.uuid, 'ma_ct_data', selectedChungTu || '');
              if (onChungTuSelect) {
                onChungTuSelect(params.row.uuid, selectedChungTu);
              }
            }
          }}
        />
      );
    }
  },
  {
    field: 'ten_ct',
    headerName: 'Tên chứng từ',
    width: 200,
    renderCell: params => (
      <CellField
        name='ten_ct'
        type='text'
        value={params.row.ma_ct_data?.ten_ct || params.row.ten_ct || ''}
        disabled={true}
      />
    )
  },
  {
    field: 'username',
    headerName: 'Người sử dụng Nhóm',
    width: 175,
    renderCell: params => {
      return (
        <SearchField<User>
          value={params.row.username || ''}
          onValueChange={(newValue: any) => {
            onCellValueChange(params.row.uuid, 'username', newValue);
            // Khi người dùng xóa giá trị, cũng xóa related field
            if (!newValue) {
              onCellValueChange(params.row.uuid, 'first_name', '');
            }
          }}
          otherSearchEndpoint={`/${QUERY_KEYS.NGUOI_SU_DUNG}`}
          searchEndpoint={``}
          searchColumns={userSearchColumns}
          dialogTitle='Danh mục người sử dụng'
          className='w-full'
          displayRelatedField='first_name'
          columnDisplay='username'
          relatedFieldValue={params.row.first_name || ''}
          onRowSelection={(selectedUser: User) => {
            if (selectedUser) {
              onCellValueChange(params.row.uuid, 'username', selectedUser.username);
              onCellValueChange(params.row.uuid, 'first_name', selectedUser.first_name);

              // Call parent callback for additional processing
              if (onUserSelect) {
                onUserSelect(params.row.uuid, selectedUser);
              }
            }
          }}
        />
      );
    }
  },
  {
    field: 'first_name',
    headerName: 'Tên người sử dụng',
    width: 200,
    renderCell: params => (
      <CellField
        name='first_name'
        type='text'
        value={params.row.first_name}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'first_name', newValue)}
        disabled={true}
      />
    )
  }
];
