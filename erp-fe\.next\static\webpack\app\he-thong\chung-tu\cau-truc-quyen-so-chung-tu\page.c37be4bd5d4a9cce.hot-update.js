"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/bottom-bar */ \"(app-pages-browser)/./src/components/custom/arito/form/bottom-bar.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InputTableTab/useInputTableRow */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/useInputTableRow.ts\");\n/* harmony import */ var _components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/custom/arito/icon */ \"(app-pages-browser)/./src/components/custom/arito/icon/index.tsx\");\n/* harmony import */ var _ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ConfirmDialog */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/ConfirmDialog.tsx\");\n/* harmony import */ var _InputTableTab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InputTableTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx\");\n/* harmony import */ var _schemas__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../schemas */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/schemas.ts\");\n/* harmony import */ var _SoHienTaiTab__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./SoHienTaiTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx\");\n/* harmony import */ var _GeneralTab__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./GeneralTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx\");\n/* harmony import */ var _BasicForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./BasicForm */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/BasicForm.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FormDialog(param) {\n    let { mode, open, onClose, onAddButtonClick, onCopyButtonClick, onDeleteButtonClick, onEditButtonClick, onSubmit, initialData } = param;\n    _s();\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [donVi, setDonVi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Chứng từ & Người sử dụng\"\n    const createGiaBanRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ma_ct: \"\",\n            ten_ct: \"\",\n            user_id: \"\",\n            username: \"\",\n            first_name: \"\" // Tên người sử dụng để hiển thị\n        };\n        return newRow;\n    }, []);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Số hiện tại\"\n    const createSoHienTaiRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ngay: \"\",\n            so_hien_tai: \"\" // Số hiện tại\n        };\n        return newRow;\n    }, []);\n    // Sử dụng hook useInputTableRow cho tab \"Chứng từ & Người sử dụng\"\n    const { rows: tableData, setRows, handleRowClick, handleAddRow: originalHandleAddRow, handleDeleteRow, handleCopyRow, handlePasteRow, handleMoveRow, handleCellValueChange: originalHandleCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createGiaBanRow);\n    const { rows: soHienTaiTableData, setRows: setSoHienTaiRows, handleRowClick: handleSoHienTaiRowClick, handleCellValueChange: originalHandleSoHienTaiCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createSoHienTaiRow);\n    // Wrap handleAddRow to add logging\n    const handleAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleAddRow();\n    }, [\n        originalHandleAddRow\n    ]);\n    // Wrap handleCellValueChange to add logging\n    const handleCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleCellValueChange\n    ]);\n    // Wrap handlers for \"Số hiện tại\" tab\n    const handleSoHienTaiCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleSoHienTaiCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleSoHienTaiCellValueChange\n    ]);\n    // State for storing selected data following GeneralTab pattern\n    const [selectedChungTuData, setSelectedChungTuData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedUserData, setSelectedUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Callback handlers following GeneralTab pattern\n    const handleChungTuSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, chungTu)=>{\n        setSelectedChungTuData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: chungTu\n            };\n            return updated;\n        });\n    }, []);\n    const handleUserSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, user)=>{\n        setSelectedUserData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: user\n            };\n            return updated;\n        });\n    }, []);\n    const handleSubmit = async (data)=>{\n        console.log(\"Form data>>>>>>>>>>>:\", data);\n        console.log(\"Table data>>>>>>>>>>>:\", tableData);\n        // Create combined list with ma_ct and username format for \"Chứng từ & Người sử dụng\" tab\n        // Map to chi_tiet format for API submission\n        const combinedList = tableData.map((row, index)=>{\n            var _user_profile;\n            const chungTu = selectedChungTuData[row.uuid];\n            const user = selectedUserData[row.uuid];\n            return {\n                line: index + 1,\n                ma_ct: (chungTu === null || chungTu === void 0 ? void 0 : chungTu.uuid) || \"\",\n                username: (user === null || user === void 0 ? void 0 : user.username) || row.username || \"\",\n                user_id: (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.uuid) || null // Use profile UUID instead of integer ID\n            };\n        });\n        // Create combined list for \"Số hiện tại\" tab\n        const soHienTaiCombinedList = soHienTaiTableData.map((row)=>{\n            return {\n                ngay: row.ngay || \"\",\n                so_hien_tai: row.so_hien_tai || \"\"\n            };\n        });\n        // Log the combined lists in requested format\n        console.log(\"\\uD83D\\uDCCB\\uD83D\\uDC65 List of ChungTu and User:\", combinedList);\n        try {\n            if (onSubmit) {\n                const formData = {\n                    ...data,\n                    danh_sach_chung_tu: combinedList,\n                    danh_sach_so_hien_tai: soHienTaiCombinedList\n                };\n                await onSubmit(formData);\n            } else {\n                setError(\"Kh\\xf4ng thể lưu dữ liệu: Kh\\xf4ng c\\xf3 xử l\\xfd submit\");\n            }\n        } catch (err) {\n            setError(err.message || \"C\\xf3 lỗi xảy ra\");\n        }\n    };\n    const handleCloseDialog = ()=>{\n        setShowConfirmDialog(false);\n        onClose();\n    };\n    // Effect to update form fields when initialData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            // Process initialData.chi_tiet if available (map from API response)\n            if (initialData.chi_tiet && Array.isArray(initialData.chi_tiet) && initialData.chi_tiet.length > 0) {\n                // Convert chi_tiet array to the format expected by the table\n                const tableRows = initialData.chi_tiet.map((item)=>{\n                    var _item_user_id_data, _item_user_id_data1;\n                    // Create a new row with a unique UUID\n                    const newRow = createGiaBanRow();\n                    newRow.ma_ct = item.ma_ct || \"\";\n                    newRow.ten_ct = item.ten_ct || \"\";\n                    newRow.user_id = item.user_id || \"\"; // This will now be UUID from profile\n                    newRow.username = item.username || \"\";\n                    newRow.first_name = ((_item_user_id_data = item.user_id_data) === null || _item_user_id_data === void 0 ? void 0 : _item_user_id_data.nickname) || ((_item_user_id_data1 = item.user_id_data) === null || _item_user_id_data1 === void 0 ? void 0 : _item_user_id_data1.first_name) || \"\";\n                    // Store the selected data for form submission (following GeneralTab pattern)\n                    if (item.ma_ct) {\n                        setSelectedChungTuData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item\n                            }));\n                    }\n                    if (item.username && item.user_id_data) {\n                        setSelectedUserData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item\n                            }));\n                    }\n                    return newRow;\n                });\n                // Set the table data\n                if (tableRows.length > 0) {\n                    // Use the setRows function from useInputTableRow\n                    setRows(tableRows);\n                }\n            }\n            // Process initialData.danh_sach_so_hien_tai if available\n            if (initialData.danh_sach_so_hien_tai && Array.isArray(initialData.danh_sach_so_hien_tai) && initialData.danh_sach_so_hien_tai.length > 0) {\n                // Convert danh_sach_so_hien_tai array to the format expected by the table\n                const soHienTaiTableRows = initialData.danh_sach_so_hien_tai.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createSoHienTaiRow();\n                    // Set the values from the danh_sach_so_hien_tai item\n                    newRow.ngay = item.ngay || \"\";\n                    newRow.so_hien_tai = item.so_hien_tai || \"\";\n                    return newRow;\n                });\n                // Set the table data for \"Số hiện tại\" tab\n                if (soHienTaiTableRows.length > 0) {\n                    setSoHienTaiRows(soHienTaiTableRows);\n                }\n            }\n        }\n    }, [\n        initialData,\n        setRows,\n        setSoHienTaiRows,\n        createGiaBanRow,\n        createSoHienTaiRow\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoDialog, {\n                open: open,\n                onClose: onClose,\n                title: mode === \"add\" ? \"Mới\" : mode === \"edit\" ? \"Sửa\" : \"Xem\",\n                maxWidth: \"lg\",\n                disableBackdropClose: false,\n                disableEscapeKeyDown: false,\n                titleIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__.AritoIcon, {\n                    icon: 281\n                }, void 0, false, void 0, void 0),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoForm, {\n                        mode: mode,\n                        hasAritoActionBar: false,\n                        schema: _schemas__WEBPACK_IMPORTED_MODULE_8__.formSchema,\n                        onSubmit: (data)=>{\n                            handleSubmit(data);\n                        },\n                        initialData: initialData,\n                        className: \"w-[50vw]\",\n                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BasicForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            mode: mode\n                        }, void 0, false, void 0, void 0),\n                        classNameBottomBar: \"relative w-full flex justify-end gap-2\",\n                        tabs: [\n                            {\n                                id: \"1\",\n                                label: \"Th\\xf4ng tin chung\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GeneralTab__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    mode: mode,\n                                    donVi: donVi,\n                                    setDonVi: setDonVi\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"3\",\n                                label: \"Chứng từ & Người sử dụng\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputTableTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    mode: mode,\n                                    rows: tableData,\n                                    onRowClick: handleRowClick,\n                                    onAddRow: handleAddRow,\n                                    onDeleteRow: handleDeleteRow,\n                                    onCopyRow: handleCopyRow,\n                                    onPasteRow: handlePasteRow,\n                                    onMoveRow: handleMoveRow,\n                                    onCellValueChange: handleCellValueChange,\n                                    onChungTuSelect: handleChungTuSelect,\n                                    onUserSelect: handleUserSelect\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"4\",\n                                label: \"Số hiện tại\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SoHienTaiTab__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    mode: mode,\n                                    rows: soHienTaiTableData,\n                                    onRowClick: handleSoHienTaiRowClick,\n                                    onCellValueChange: handleSoHienTaiCellValueChange\n                                }, void 0, false, void 0, void 0)\n                            }\n                        ],\n                        bottomBar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__.BottomBar, {\n                            mode: mode,\n                            onAdd: onAddButtonClick,\n                            onEdit: onEditButtonClick,\n                            onDelete: onDeleteButtonClick,\n                            onCopy: onCopyButtonClick,\n                            onClose: onClose\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-4 mb-4 rounded bg-red-100 p-2 text-red-700\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseDialog,\n                open: showConfirmDialog,\n                onCloseConfirmDialog: ()=>setShowConfirmDialog(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FormDialog, \"AE6QFM1+LCL/ZsIU5CFTwcEJQvY=\", false, function() {\n    return [\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = FormDialog;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FormDialog);\nvar _c;\n$RefreshReg$(_c, \"FormDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9oZS10aG9uZy9jaHVuZy10dS9jYXUtdHJ1Yy1xdXllbi1zby1jaHVuZy10dS9jb21wb25lbnRzL2RpYWxvZy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUF5RDtBQUNhO0FBQ0g7QUFDSDtBQUNMO0FBRWQ7QUFDRDtBQUNEO0FBQ0Q7QUFFSjtBQUNGO0FBcUNwQyxTQUFTYyxXQUFXLEtBVUY7UUFWRSxFQUNsQkMsSUFBSSxFQUNKQyxJQUFJLEVBQ0pDLE9BQU8sRUFDUEMsZ0JBQWdCLEVBQ2hCQyxpQkFBaUIsRUFDakJDLG1CQUFtQixFQUNuQkMsaUJBQWlCLEVBQ2pCQyxRQUFRLEVBQ1JDLFdBQVcsRUFDSyxHQVZFOztJQVdsQixNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUMwQixPQUFPQyxTQUFTLEdBQUczQiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsTUFBTSxDQUFDNEIsT0FBT0MsU0FBUyxHQUFHN0IsK0NBQVFBLENBQW1CO0lBRXJELGlFQUFpRTtJQUNqRSxNQUFNOEIsa0JBQWtCNUIsa0RBQVdBLENBQUM7UUFDbEMsTUFBTTZCLFNBQVM7WUFDYkMsTUFBTUMsT0FBT0MsS0FBS0MsTUFBTTtZQUN4QkMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxZQUFZLEdBQUcsZ0NBQWdDO1FBQ2pEO1FBQ0EsT0FBT1Q7SUFDVCxHQUFHLEVBQUU7SUFFTCxvREFBb0Q7SUFDcEQsTUFBTVUscUJBQXFCdkMsa0RBQVdBLENBQUM7UUFDckMsTUFBTTZCLFNBQVM7WUFDYkMsTUFBTUMsT0FBT0MsS0FBS0MsTUFBTTtZQUN4Qk8sTUFBTTtZQUNOQyxhQUFhLEdBQUcsY0FBYztRQUNoQztRQUNBLE9BQU9aO0lBQ1QsR0FBRyxFQUFFO0lBRUwsbUVBQW1FO0lBQ25FLE1BQU0sRUFDSmEsTUFBTUMsU0FBUyxFQUNmQyxPQUFPLEVBQ1BDLGNBQWMsRUFDZEMsY0FBY0Msb0JBQW9CLEVBQ2xDQyxlQUFlLEVBQ2ZDLGFBQWEsRUFDYkMsY0FBYyxFQUNkQyxhQUFhLEVBQ2JDLHVCQUF1QkMsNkJBQTZCLEVBQ3JELEdBQUdqRCwyRUFBZ0JBLENBQU0sRUFBRSxFQUFFd0I7SUFFOUIsTUFBTSxFQUNKYyxNQUFNWSxrQkFBa0IsRUFDeEJWLFNBQVNXLGdCQUFnQixFQUN6QlYsZ0JBQWdCVyx1QkFBdUIsRUFDdkNKLHVCQUF1Qkssc0NBQXNDLEVBQzlELEdBQUdyRCwyRUFBZ0JBLENBQU0sRUFBRSxFQUFFbUM7SUFFOUIsbUNBQW1DO0lBQ25DLE1BQU1PLGVBQWU5QyxrREFBV0EsQ0FBQztRQUMvQitDO0lBQ0YsR0FBRztRQUFDQTtLQUFxQjtJQUV6Qiw0Q0FBNEM7SUFDNUMsTUFBTUssd0JBQXdCcEQsa0RBQVdBLENBQ3ZDLENBQUMwRCxTQUFpQkMsT0FBZUM7UUFDL0JQLDhCQUE4QkssU0FBU0MsT0FBT0M7SUFDaEQsR0FDQTtRQUFDUDtLQUE4QjtJQUdqQyxzQ0FBc0M7SUFDdEMsTUFBTVEsaUNBQWlDN0Qsa0RBQVdBLENBQ2hELENBQUMwRCxTQUFpQkMsT0FBZUM7UUFDL0JILHVDQUF1Q0MsU0FBU0MsT0FBT0M7SUFDekQsR0FDQTtRQUFDSDtLQUF1QztJQUcxQywrREFBK0Q7SUFDL0QsTUFBTSxDQUFDSyxxQkFBcUJDLHVCQUF1QixHQUFHakUsK0NBQVFBLENBQTBCLENBQUM7SUFDekYsTUFBTSxDQUFDa0Usa0JBQWtCQyxvQkFBb0IsR0FBR25FLCtDQUFRQSxDQUF1QixDQUFDO0lBRWhGLGlEQUFpRDtJQUNqRCxNQUFNb0Usc0JBQXNCbEUsa0RBQVdBLENBQUMsQ0FBQzBELFNBQWlCUztRQUN4REosdUJBQXVCSyxDQUFBQTtZQUNyQixNQUFNQyxVQUFVO2dCQUNkLEdBQUdELElBQUk7Z0JBQ1AsQ0FBQ1YsUUFBUSxFQUFFUztZQUNiO1lBQ0EsT0FBT0U7UUFDVDtJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1DLG1CQUFtQnRFLGtEQUFXQSxDQUFDLENBQUMwRCxTQUFpQmE7UUFDckROLG9CQUFvQkcsQ0FBQUE7WUFDbEIsTUFBTUMsVUFBVTtnQkFDZCxHQUFHRCxJQUFJO2dCQUNQLENBQUNWLFFBQVEsRUFBRWE7WUFDYjtZQUVBLE9BQU9GO1FBQ1Q7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNRyxlQUFlLE9BQU9DO1FBQzFCQyxRQUFRQyxHQUFHLENBQUMseUJBQXlCRjtRQUNyQ0MsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQmhDO1FBRXRDLHlGQUF5RjtRQUN6Riw0Q0FBNEM7UUFDNUMsTUFBTWlDLGVBQWVqQyxVQUFVa0MsR0FBRyxDQUFDLENBQUNDLEtBQUtDO2dCQVE1QlI7WUFQWCxNQUFNSixVQUFVTCxtQkFBbUIsQ0FBQ2dCLElBQUloRCxJQUFJLENBQUM7WUFDN0MsTUFBTXlDLE9BQU9QLGdCQUFnQixDQUFDYyxJQUFJaEQsSUFBSSxDQUFDO1lBRXZDLE9BQU87Z0JBQ0xrRCxNQUFNRCxRQUFRO2dCQUNkN0MsT0FBT2lDLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU3JDLElBQUksS0FBSTtnQkFDeEJPLFVBQVVrQyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1sQyxRQUFRLEtBQUl5QyxJQUFJekMsUUFBUSxJQUFJO2dCQUM1Q0QsU0FBU21DLENBQUFBLGlCQUFBQSw0QkFBQUEsZ0JBQUFBLEtBQU1VLE9BQU8sY0FBYlYsb0NBQUFBLGNBQWV6QyxJQUFJLEtBQUksS0FBSyx5Q0FBeUM7WUFDaEY7UUFDRjtRQUVBLDZDQUE2QztRQUM3QyxNQUFNb0Qsd0JBQXdCNUIsbUJBQW1CdUIsR0FBRyxDQUFDQyxDQUFBQTtZQUNuRCxPQUFPO2dCQUNMdEMsTUFBTXNDLElBQUl0QyxJQUFJLElBQUk7Z0JBQ2xCQyxhQUFhcUMsSUFBSXJDLFdBQVcsSUFBSTtZQUNsQztRQUNGO1FBRUEsNkNBQTZDO1FBQzdDaUMsUUFBUUMsR0FBRyxDQUFDLHNEQUFrQ0M7UUFFOUMsSUFBSTtZQUNGLElBQUl4RCxVQUFVO2dCQUNaLE1BQU0rRCxXQUFXO29CQUNmLEdBQUdWLElBQUk7b0JBQ1BXLG9CQUFvQlI7b0JBQ3BCUyx1QkFBdUJIO2dCQUN6QjtnQkFDQSxNQUFNOUQsU0FBUytEO1lBQ2pCLE9BQU87Z0JBQ0wxRCxTQUFTO1lBQ1g7UUFDRixFQUFFLE9BQU82RCxLQUFVO1lBQ2pCN0QsU0FBUzZELElBQUlDLE9BQU8sSUFBSTtRQUMxQjtJQUNGO0lBRUEsTUFBTUMsb0JBQW9CO1FBQ3hCakUscUJBQXFCO1FBQ3JCUjtJQUNGO0lBRUEsd0RBQXdEO0lBQ3hEaEIsZ0RBQVNBLENBQUM7UUFDUixJQUFJc0IsYUFBYTtZQUNmLG9FQUFvRTtZQUNwRSxJQUFJQSxZQUFZb0UsUUFBUSxJQUFJQyxNQUFNQyxPQUFPLENBQUN0RSxZQUFZb0UsUUFBUSxLQUFLcEUsWUFBWW9FLFFBQVEsQ0FBQ0csTUFBTSxHQUFHLEdBQUc7Z0JBQ2xHLDZEQUE2RDtnQkFDN0QsTUFBTUMsWUFBWXhFLFlBQVlvRSxRQUFRLENBQUNaLEdBQUcsQ0FBQyxDQUFDaUI7d0JBUXRCQSxvQkFBK0JBO29CQVBuRCxzQ0FBc0M7b0JBQ3RDLE1BQU1qRSxTQUFTRDtvQkFFZkMsT0FBT0ssS0FBSyxHQUFHNEQsS0FBSzVELEtBQUssSUFBSTtvQkFDN0JMLE9BQU9NLE1BQU0sR0FBRzJELEtBQUszRCxNQUFNLElBQUk7b0JBQy9CTixPQUFPTyxPQUFPLEdBQUcwRCxLQUFLMUQsT0FBTyxJQUFJLElBQUkscUNBQXFDO29CQUMxRVAsT0FBT1EsUUFBUSxHQUFHeUQsS0FBS3pELFFBQVEsSUFBSTtvQkFDbkNSLE9BQU9TLFVBQVUsR0FBR3dELEVBQUFBLHFCQUFBQSxLQUFLQyxZQUFZLGNBQWpCRCx5Q0FBQUEsbUJBQW1CRSxRQUFRLE9BQUlGLHNCQUFBQSxLQUFLQyxZQUFZLGNBQWpCRCwwQ0FBQUEsb0JBQW1CeEQsVUFBVSxLQUFJO29CQUVwRiw2RUFBNkU7b0JBQzdFLElBQUl3RCxLQUFLNUQsS0FBSyxFQUFFO3dCQUNkNkIsdUJBQXVCSyxDQUFBQSxPQUFTO2dDQUM5QixHQUFHQSxJQUFJO2dDQUNQLENBQUN2QyxPQUFPQyxJQUFJLENBQUMsRUFBRWdFOzRCQUNqQjtvQkFDRjtvQkFFQSxJQUFJQSxLQUFLekQsUUFBUSxJQUFJeUQsS0FBS0MsWUFBWSxFQUFFO3dCQUN0QzlCLG9CQUFvQkcsQ0FBQUEsT0FBUztnQ0FDM0IsR0FBR0EsSUFBSTtnQ0FDUCxDQUFDdkMsT0FBT0MsSUFBSSxDQUFDLEVBQUVnRTs0QkFDakI7b0JBQ0Y7b0JBRUEsT0FBT2pFO2dCQUNUO2dCQUVBLHFCQUFxQjtnQkFDckIsSUFBSWdFLFVBQVVELE1BQU0sR0FBRyxHQUFHO29CQUN4QixpREFBaUQ7b0JBQ2pEaEQsUUFBUWlEO2dCQUNWO1lBQ0Y7WUFFQSx5REFBeUQ7WUFDekQsSUFDRXhFLFlBQVlnRSxxQkFBcUIsSUFDakNLLE1BQU1DLE9BQU8sQ0FBQ3RFLFlBQVlnRSxxQkFBcUIsS0FDL0NoRSxZQUFZZ0UscUJBQXFCLENBQUNPLE1BQU0sR0FBRyxHQUMzQztnQkFDQSwwRUFBMEU7Z0JBQzFFLE1BQU1LLHFCQUFxQjVFLFlBQVlnRSxxQkFBcUIsQ0FBQ1IsR0FBRyxDQUFDLENBQUNpQjtvQkFDaEUsc0NBQXNDO29CQUN0QyxNQUFNakUsU0FBU1U7b0JBRWYscURBQXFEO29CQUNyRFYsT0FBT1csSUFBSSxHQUFHc0QsS0FBS3RELElBQUksSUFBSTtvQkFDM0JYLE9BQU9ZLFdBQVcsR0FBR3FELEtBQUtyRCxXQUFXLElBQUk7b0JBRXpDLE9BQU9aO2dCQUNUO2dCQUVBLDJDQUEyQztnQkFDM0MsSUFBSW9FLG1CQUFtQkwsTUFBTSxHQUFHLEdBQUc7b0JBQ2pDckMsaUJBQWlCMEM7Z0JBQ25CO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQzVFO1FBQWF1QjtRQUFTVztRQUFrQjNCO1FBQWlCVztLQUFtQjtJQUVoRixxQkFDRTs7MEJBQ0UsOERBQUNyQyxpRUFBV0E7Z0JBQ1ZZLE1BQU1BO2dCQUNOQyxTQUFTQTtnQkFDVG1GLE9BQU9yRixTQUFTLFFBQVEsUUFBUUEsU0FBUyxTQUFTLFFBQVE7Z0JBQzFEc0YsVUFBUztnQkFDVEMsc0JBQXNCO2dCQUN0QkMsc0JBQXNCO2dCQUN0QkMseUJBQVcsOERBQUNqRyxvRUFBU0E7b0JBQUNrRyxNQUFNOzs7a0NBRTVCLDhEQUFDcEcsK0RBQVNBO3dCQUNSVSxNQUFNQTt3QkFDTjJGLG1CQUFtQjt3QkFDbkJDLFFBQVFqRyxnREFBVUE7d0JBQ2xCWSxVQUFVcUQsQ0FBQUE7NEJBQ1JELGFBQWFDO3dCQUNmO3dCQUNBcEQsYUFBYUE7d0JBQ2JxRixXQUFVO3dCQUNWQyw0QkFBYyw4REFBQ2hHLG1EQUFTQTs0QkFBQ0UsTUFBTUE7O3dCQUMvQitGLG9CQUFtQjt3QkFDbkJDLE1BQU07NEJBQ0o7Z0NBQ0VDLElBQUk7Z0NBQ0pDLE9BQU87Z0NBQ1BDLHlCQUFXLDhEQUFDdEcsb0RBQVVBO29DQUFDRyxNQUFNQTtvQ0FBTWEsT0FBT0E7b0NBQU9DLFVBQVVBOzs0QkFDN0Q7NEJBQ0E7Z0NBQ0VtRixJQUFJO2dDQUNKQyxPQUFPO2dDQUNQQyx5QkFDRSw4REFBQ3pHLHNEQUFhQTtvQ0FDWk0sTUFBTUE7b0NBQ042QixNQUFNQztvQ0FDTnNFLFlBQVlwRTtvQ0FDWnFFLFVBQVVwRTtvQ0FDVnFFLGFBQWFuRTtvQ0FDYm9FLFdBQVduRTtvQ0FDWG9FLFlBQVluRTtvQ0FDWm9FLFdBQVduRTtvQ0FDWG9FLG1CQUFtQm5FO29DQUNuQm9FLGlCQUFpQnREO29DQUNqQnVELGNBQWNuRDs7NEJBR3BCOzRCQUNBO2dDQUNFd0MsSUFBSTtnQ0FDSkMsT0FBTztnQ0FDUEMseUJBQ0UsOERBQUN2RyxxREFBWUE7b0NBQ1hJLE1BQU1BO29DQUNONkIsTUFBTVk7b0NBQ04yRCxZQUFZekQ7b0NBQ1orRCxtQkFBbUIxRDs7NEJBR3pCO3lCQUNEO3dCQUNENkQseUJBQ0UsOERBQUN6SCwrRUFBU0E7NEJBQ1JZLE1BQU1BOzRCQUNOOEcsT0FBTzNHOzRCQUNQNEcsUUFBUXpHOzRCQUNSMEcsVUFBVTNHOzRCQUNWNEcsUUFBUTdHOzRCQUNSRixTQUFTQTs7Ozs7OztvQkFJZFMsdUJBQVMsOERBQUN1Rzt3QkFBSXJCLFdBQVU7a0NBQWlEbEY7Ozs7Ozs7Ozs7OzswQkFHNUUsOERBQUNsQixzREFBYUE7Z0JBQ1pTLFNBQVN5RTtnQkFDVDFFLE1BQU1RO2dCQUNOMEcsc0JBQXNCLElBQU16RyxxQkFBcUI7Ozs7Ozs7O0FBSXpEO0dBaFRTWDs7UUFrREhSLHVFQUFnQkE7UUFPaEJBLHVFQUFnQkE7OztLQXpEYlE7QUFrVFQsK0RBQWVBLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2ZlYXR1cmVzL2hlLXRob25nL2NodW5nLXR1L2NhdS10cnVjLXF1eWVuLXNvLWNodW5nLXR1L2NvbXBvbmVudHMvZGlhbG9nL2luZGV4LnRzeD9jNjJhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBCb3R0b21CYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvL2Zvcm0vYm90dG9tLWJhcic7XHJcbmltcG9ydCB7IEFyaXRvRGlhbG9nLCBBcml0b0Zvcm0gfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvJztcclxuaW1wb3J0IHVzZUlucHV0VGFibGVSb3cgZnJvbSAnLi9JbnB1dFRhYmxlVGFiL3VzZUlucHV0VGFibGVSb3cnO1xyXG5pbXBvcnQgeyBBcml0b0ljb24gfSBmcm9tICdAL2NvbXBvbmVudHMvY3VzdG9tL2FyaXRvL2ljb24nO1xyXG5pbXBvcnQgeyBEb25WaUNvU28sIENodW5nVHUgfSBmcm9tICdAL3R5cGVzL3NjaGVtYXMnO1xyXG5pbXBvcnQgQ29uZmlybURpYWxvZyBmcm9tICcuLi9Db25maXJtRGlhbG9nJztcclxuaW1wb3J0IElucHV0VGFibGVUYWIgZnJvbSAnLi9JbnB1dFRhYmxlVGFiJztcclxuaW1wb3J0IHsgZm9ybVNjaGVtYSB9IGZyb20gJy4uLy4uL3NjaGVtYXMnO1xyXG5pbXBvcnQgU29IaWVuVGFpVGFiIGZyb20gJy4vU29IaWVuVGFpVGFiJztcclxuaW1wb3J0IHsgRm9ybU1vZGUgfSBmcm9tICdAL3R5cGVzL2Zvcm0nO1xyXG5pbXBvcnQgR2VuZXJhbFRhYiBmcm9tICcuL0dlbmVyYWxUYWInO1xyXG5pbXBvcnQgQmFzaWNGb3JtIGZyb20gJy4vQmFzaWNGb3JtJztcclxuXHJcbi8vIEludGVyZmFjZSBmb3IgVXNlciBkYXRhIChtYXRjaGluZyBiYWNrZW5kIHN0cnVjdHVyZSB3aXRoIHByb2ZpbGUpXHJcbmludGVyZmFjZSBVc2VyUHJvZmlsZSB7XHJcbiAgdXVpZDogc3RyaW5nO1xyXG4gIG5pY2tuYW1lPzogc3RyaW5nIHwgbnVsbDtcclxufVxyXG5cclxuaW50ZXJmYWNlIFVzZXIge1xyXG4gIGlkOiBudW1iZXI7XHJcbiAgdXNlcm5hbWU6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG4gIGZpcnN0X25hbWU6IHN0cmluZztcclxuICBsYXN0X25hbWU/OiBzdHJpbmc7XHJcbiAgaXNfc3RhZmY/OiBib29sZWFuO1xyXG4gIGlzX2FjdGl2ZT86IGJvb2xlYW47XHJcbiAgZGF0ZV9qb2luZWQ/OiBzdHJpbmc7XHJcbiAgcHJvZmlsZTogVXNlclByb2ZpbGU7XHJcbn1cclxuXHJcbmludGVyZmFjZSBGb3JtRGlhbG9nUHJvcHMge1xyXG4gIG1vZGU6IEZvcm1Nb2RlO1xyXG4gIG9wZW46IGJvb2xlYW47XHJcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcclxuXHJcbiAgb25BZGRCdXR0b25DbGljaz86ICgpID0+IHZvaWQ7XHJcbiAgb25FZGl0QnV0dG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG4gIG9uRGVsZXRlQnV0dG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG4gIG9uQ29weUJ1dHRvbkNsaWNrPzogKCkgPT4gdm9pZDtcclxuICBvbldhdGNoQnV0dG9uQ2xpY2s/OiAoKSA9PiB2b2lkO1xyXG5cclxuICAvLyBQYXJlbnQgY29tcG9uZW50J3Mgc3VibWl0IGhhbmRsZXIgLSB1cGRhdGVkIHRvIHVzZSBuZXcgZGF0YSBzdHJ1Y3R1cmVcclxuICBvblN1Ym1pdD86IChkYXRhOiBhbnkpID0+IHZvaWQ7XHJcblxyXG4gIGluaXRpYWxEYXRhPzogYW55O1xyXG59XHJcblxyXG5mdW5jdGlvbiBGb3JtRGlhbG9nKHtcclxuICBtb2RlLFxyXG4gIG9wZW4sXHJcbiAgb25DbG9zZSxcclxuICBvbkFkZEJ1dHRvbkNsaWNrLFxyXG4gIG9uQ29weUJ1dHRvbkNsaWNrLFxyXG4gIG9uRGVsZXRlQnV0dG9uQ2xpY2ssXHJcbiAgb25FZGl0QnV0dG9uQ2xpY2ssXHJcbiAgb25TdWJtaXQsXHJcbiAgaW5pdGlhbERhdGFcclxufTogRm9ybURpYWxvZ1Byb3BzKSB7XHJcbiAgY29uc3QgW3Nob3dDb25maXJtRGlhbG9nLCBzZXRTaG93Q29uZmlybURpYWxvZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuXHJcbiAgY29uc3QgW2RvblZpLCBzZXREb25WaV0gPSB1c2VTdGF0ZTxEb25WaUNvU28gfCBudWxsPihudWxsKTtcclxuXHJcbiAgLy8gSMOgbSB04bqhbyBkw7JuZyBt4bubaSB0w7l5IGNo4buJbmggY2hvIGLhuqNuZyBcIkNo4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nXCJcclxuICBjb25zdCBjcmVhdGVHaWFCYW5Sb3cgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBjb25zdCBuZXdSb3cgPSB7XHJcbiAgICAgIHV1aWQ6IFN0cmluZyhNYXRoLnJhbmRvbSgpKSxcclxuICAgICAgbWFfY3Q6ICcnLCAvLyBNw6MgY2jhu6luZyB04burIMSR4buDIGhp4buDbiB0aOG7i1xyXG4gICAgICB0ZW5fY3Q6ICcnLCAvLyBUw6puIGNo4bupbmcgdOG7qyDEkeG7gyBoaeG7g24gdGjhu4tcclxuICAgICAgdXNlcl9pZDogJycsIC8vIFVVSUQgbmfGsOG7nWkgc+G7rSBk4bulbmcgdOG7qyBwcm9maWxlIChpbnRlcm5hbClcclxuICAgICAgdXNlcm5hbWU6ICcnLCAvLyBVc2VybmFtZSDEkeG7gyBoaeG7g24gdGjhu4tcclxuICAgICAgZmlyc3RfbmFtZTogJycgLy8gVMOqbiBuZ8aw4budaSBz4butIGThu6VuZyDEkeG7gyBoaeG7g24gdGjhu4tcclxuICAgIH07XHJcbiAgICByZXR1cm4gbmV3Um93O1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gSMOgbSB04bqhbyBkw7JuZyBt4bubaSB0w7l5IGNo4buJbmggY2hvIGLhuqNuZyBcIlPhu5EgaGnhu4duIHThuqFpXCJcclxuICBjb25zdCBjcmVhdGVTb0hpZW5UYWlSb3cgPSB1c2VDYWxsYmFjaygoKSA9PiB7XHJcbiAgICBjb25zdCBuZXdSb3cgPSB7XHJcbiAgICAgIHV1aWQ6IFN0cmluZyhNYXRoLnJhbmRvbSgpKSxcclxuICAgICAgbmdheTogJycsIC8vIE5nw6B5XHJcbiAgICAgIHNvX2hpZW5fdGFpOiAnJyAvLyBT4buRIGhp4buHbiB04bqhaVxyXG4gICAgfTtcclxuICAgIHJldHVybiBuZXdSb3c7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBT4butIGThu6VuZyBob29rIHVzZUlucHV0VGFibGVSb3cgY2hvIHRhYiBcIkNo4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nXCJcclxuICBjb25zdCB7XHJcbiAgICByb3dzOiB0YWJsZURhdGEsXHJcbiAgICBzZXRSb3dzLCAvLyBVbmNvbW1lbnQgdG8gdXNlIGZvciBzZXR0aW5nIHRhYmxlIGRhdGEgZnJvbSBpbml0aWFsRGF0YVxyXG4gICAgaGFuZGxlUm93Q2xpY2ssXHJcbiAgICBoYW5kbGVBZGRSb3c6IG9yaWdpbmFsSGFuZGxlQWRkUm93LFxyXG4gICAgaGFuZGxlRGVsZXRlUm93LFxyXG4gICAgaGFuZGxlQ29weVJvdyxcclxuICAgIGhhbmRsZVBhc3RlUm93LFxyXG4gICAgaGFuZGxlTW92ZVJvdyxcclxuICAgIGhhbmRsZUNlbGxWYWx1ZUNoYW5nZTogb3JpZ2luYWxIYW5kbGVDZWxsVmFsdWVDaGFuZ2VcclxuICB9ID0gdXNlSW5wdXRUYWJsZVJvdzxhbnk+KFtdLCBjcmVhdGVHaWFCYW5Sb3cpO1xyXG5cclxuICBjb25zdCB7XHJcbiAgICByb3dzOiBzb0hpZW5UYWlUYWJsZURhdGEsXHJcbiAgICBzZXRSb3dzOiBzZXRTb0hpZW5UYWlSb3dzLFxyXG4gICAgaGFuZGxlUm93Q2xpY2s6IGhhbmRsZVNvSGllblRhaVJvd0NsaWNrLFxyXG4gICAgaGFuZGxlQ2VsbFZhbHVlQ2hhbmdlOiBvcmlnaW5hbEhhbmRsZVNvSGllblRhaUNlbGxWYWx1ZUNoYW5nZVxyXG4gIH0gPSB1c2VJbnB1dFRhYmxlUm93PGFueT4oW10sIGNyZWF0ZVNvSGllblRhaVJvdyk7XHJcblxyXG4gIC8vIFdyYXAgaGFuZGxlQWRkUm93IHRvIGFkZCBsb2dnaW5nXHJcbiAgY29uc3QgaGFuZGxlQWRkUm93ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xyXG4gICAgb3JpZ2luYWxIYW5kbGVBZGRSb3coKTtcclxuICB9LCBbb3JpZ2luYWxIYW5kbGVBZGRSb3ddKTtcclxuXHJcbiAgLy8gV3JhcCBoYW5kbGVDZWxsVmFsdWVDaGFuZ2UgdG8gYWRkIGxvZ2dpbmdcclxuICBjb25zdCBoYW5kbGVDZWxsVmFsdWVDaGFuZ2UgPSB1c2VDYWxsYmFjayhcclxuICAgIChyb3dVdWlkOiBzdHJpbmcsIGZpZWxkOiBzdHJpbmcsIG5ld1ZhbHVlOiBhbnkpID0+IHtcclxuICAgICAgb3JpZ2luYWxIYW5kbGVDZWxsVmFsdWVDaGFuZ2Uocm93VXVpZCwgZmllbGQsIG5ld1ZhbHVlKTtcclxuICAgIH0sXHJcbiAgICBbb3JpZ2luYWxIYW5kbGVDZWxsVmFsdWVDaGFuZ2VdXHJcbiAgKTtcclxuXHJcbiAgLy8gV3JhcCBoYW5kbGVycyBmb3IgXCJT4buRIGhp4buHbiB04bqhaVwiIHRhYlxyXG4gIGNvbnN0IGhhbmRsZVNvSGllblRhaUNlbGxWYWx1ZUNoYW5nZSA9IHVzZUNhbGxiYWNrKFxyXG4gICAgKHJvd1V1aWQ6IHN0cmluZywgZmllbGQ6IHN0cmluZywgbmV3VmFsdWU6IGFueSkgPT4ge1xyXG4gICAgICBvcmlnaW5hbEhhbmRsZVNvSGllblRhaUNlbGxWYWx1ZUNoYW5nZShyb3dVdWlkLCBmaWVsZCwgbmV3VmFsdWUpO1xyXG4gICAgfSxcclxuICAgIFtvcmlnaW5hbEhhbmRsZVNvSGllblRhaUNlbGxWYWx1ZUNoYW5nZV1cclxuICApO1xyXG5cclxuICAvLyBTdGF0ZSBmb3Igc3RvcmluZyBzZWxlY3RlZCBkYXRhIGZvbGxvd2luZyBHZW5lcmFsVGFiIHBhdHRlcm5cclxuICBjb25zdCBbc2VsZWN0ZWRDaHVuZ1R1RGF0YSwgc2V0U2VsZWN0ZWRDaHVuZ1R1RGF0YV0gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBDaHVuZ1R1Pj4oe30pO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFVzZXJEYXRhLCBzZXRTZWxlY3RlZFVzZXJEYXRhXSA9IHVzZVN0YXRlPFJlY29yZDxzdHJpbmcsIFVzZXI+Pih7fSk7XHJcblxyXG4gIC8vIENhbGxiYWNrIGhhbmRsZXJzIGZvbGxvd2luZyBHZW5lcmFsVGFiIHBhdHRlcm5cclxuICBjb25zdCBoYW5kbGVDaHVuZ1R1U2VsZWN0ID0gdXNlQ2FsbGJhY2soKHJvd1V1aWQ6IHN0cmluZywgY2h1bmdUdTogQ2h1bmdUdSkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRDaHVuZ1R1RGF0YShwcmV2ID0+IHtcclxuICAgICAgY29uc3QgdXBkYXRlZCA9IHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIFtyb3dVdWlkXTogY2h1bmdUdVxyXG4gICAgICB9O1xyXG4gICAgICByZXR1cm4gdXBkYXRlZDtcclxuICAgIH0pO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVXNlclNlbGVjdCA9IHVzZUNhbGxiYWNrKChyb3dVdWlkOiBzdHJpbmcsIHVzZXI6IFVzZXIpID0+IHtcclxuICAgIHNldFNlbGVjdGVkVXNlckRhdGEocHJldiA9PiB7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICBbcm93VXVpZF06IHVzZXJcclxuICAgICAgfTtcclxuXHJcbiAgICAgIHJldHVybiB1cGRhdGVkO1xyXG4gICAgfSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZGF0YTogYW55KSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnRm9ybSBkYXRhPj4+Pj4+Pj4+Pj46JywgZGF0YSk7XHJcbiAgICBjb25zb2xlLmxvZygnVGFibGUgZGF0YT4+Pj4+Pj4+Pj4+OicsIHRhYmxlRGF0YSk7XHJcblxyXG4gICAgLy8gQ3JlYXRlIGNvbWJpbmVkIGxpc3Qgd2l0aCBtYV9jdCBhbmQgdXNlcm5hbWUgZm9ybWF0IGZvciBcIkNo4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nXCIgdGFiXHJcbiAgICAvLyBNYXAgdG8gY2hpX3RpZXQgZm9ybWF0IGZvciBBUEkgc3VibWlzc2lvblxyXG4gICAgY29uc3QgY29tYmluZWRMaXN0ID0gdGFibGVEYXRhLm1hcCgocm93LCBpbmRleCkgPT4ge1xyXG4gICAgICBjb25zdCBjaHVuZ1R1ID0gc2VsZWN0ZWRDaHVuZ1R1RGF0YVtyb3cudXVpZF07XHJcbiAgICAgIGNvbnN0IHVzZXIgPSBzZWxlY3RlZFVzZXJEYXRhW3Jvdy51dWlkXTtcclxuXHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgbGluZTogaW5kZXggKyAxLFxyXG4gICAgICAgIG1hX2N0OiBjaHVuZ1R1Py51dWlkIHx8ICcnLCAvLyBVc2UgQ2h1bmdUdSBVVUlEIGluc3RlYWQgb2YgZGlzcGxheSBjb2RlXHJcbiAgICAgICAgdXNlcm5hbWU6IHVzZXI/LnVzZXJuYW1lIHx8IHJvdy51c2VybmFtZSB8fCAnJyxcclxuICAgICAgICB1c2VyX2lkOiB1c2VyPy5wcm9maWxlPy51dWlkIHx8IG51bGwgLy8gVXNlIHByb2ZpbGUgVVVJRCBpbnN0ZWFkIG9mIGludGVnZXIgSURcclxuICAgICAgfTtcclxuICAgIH0pO1xyXG5cclxuICAgIC8vIENyZWF0ZSBjb21iaW5lZCBsaXN0IGZvciBcIlPhu5EgaGnhu4duIHThuqFpXCIgdGFiXHJcbiAgICBjb25zdCBzb0hpZW5UYWlDb21iaW5lZExpc3QgPSBzb0hpZW5UYWlUYWJsZURhdGEubWFwKHJvdyA9PiB7XHJcbiAgICAgIHJldHVybiB7XHJcbiAgICAgICAgbmdheTogcm93Lm5nYXkgfHwgJycsXHJcbiAgICAgICAgc29faGllbl90YWk6IHJvdy5zb19oaWVuX3RhaSB8fCAnJ1xyXG4gICAgICB9O1xyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gTG9nIHRoZSBjb21iaW5lZCBsaXN0cyBpbiByZXF1ZXN0ZWQgZm9ybWF0XHJcbiAgICBjb25zb2xlLmxvZygn8J+Ti/CfkaUgTGlzdCBvZiBDaHVuZ1R1IGFuZCBVc2VyOicsIGNvbWJpbmVkTGlzdCk7XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgaWYgKG9uU3VibWl0KSB7XHJcbiAgICAgICAgY29uc3QgZm9ybURhdGEgPSB7XHJcbiAgICAgICAgICAuLi5kYXRhLFxyXG4gICAgICAgICAgZGFuaF9zYWNoX2NodW5nX3R1OiBjb21iaW5lZExpc3QsXHJcbiAgICAgICAgICBkYW5oX3NhY2hfc29faGllbl90YWk6IHNvSGllblRhaUNvbWJpbmVkTGlzdFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgYXdhaXQgb25TdWJtaXQoZm9ybURhdGEpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldEVycm9yKCdLaMO0bmcgdGjhu4MgbMawdSBk4buvIGxp4buHdTogS2jDtG5nIGPDsyB44butIGzDvSBzdWJtaXQnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcclxuICAgICAgc2V0RXJyb3IoZXJyLm1lc3NhZ2UgfHwgJ0PDsyBs4buXaSB44bqjeSByYScpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNsb3NlRGlhbG9nID0gKCkgPT4ge1xyXG4gICAgc2V0U2hvd0NvbmZpcm1EaWFsb2coZmFsc2UpO1xyXG4gICAgb25DbG9zZSgpO1xyXG4gIH07XHJcblxyXG4gIC8vIEVmZmVjdCB0byB1cGRhdGUgZm9ybSBmaWVsZHMgd2hlbiBpbml0aWFsRGF0YSBjaGFuZ2VzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpbml0aWFsRGF0YSkge1xyXG4gICAgICAvLyBQcm9jZXNzIGluaXRpYWxEYXRhLmNoaV90aWV0IGlmIGF2YWlsYWJsZSAobWFwIGZyb20gQVBJIHJlc3BvbnNlKVxyXG4gICAgICBpZiAoaW5pdGlhbERhdGEuY2hpX3RpZXQgJiYgQXJyYXkuaXNBcnJheShpbml0aWFsRGF0YS5jaGlfdGlldCkgJiYgaW5pdGlhbERhdGEuY2hpX3RpZXQubGVuZ3RoID4gMCkge1xyXG4gICAgICAgIC8vIENvbnZlcnQgY2hpX3RpZXQgYXJyYXkgdG8gdGhlIGZvcm1hdCBleHBlY3RlZCBieSB0aGUgdGFibGVcclxuICAgICAgICBjb25zdCB0YWJsZVJvd3MgPSBpbml0aWFsRGF0YS5jaGlfdGlldC5tYXAoKGl0ZW06IGFueSkgPT4ge1xyXG4gICAgICAgICAgLy8gQ3JlYXRlIGEgbmV3IHJvdyB3aXRoIGEgdW5pcXVlIFVVSURcclxuICAgICAgICAgIGNvbnN0IG5ld1JvdyA9IGNyZWF0ZUdpYUJhblJvdygpO1xyXG5cclxuICAgICAgICAgIG5ld1Jvdy5tYV9jdCA9IGl0ZW0ubWFfY3QgfHwgJyc7XHJcbiAgICAgICAgICBuZXdSb3cudGVuX2N0ID0gaXRlbS50ZW5fY3QgfHwgJyc7XHJcbiAgICAgICAgICBuZXdSb3cudXNlcl9pZCA9IGl0ZW0udXNlcl9pZCB8fCAnJzsgLy8gVGhpcyB3aWxsIG5vdyBiZSBVVUlEIGZyb20gcHJvZmlsZVxyXG4gICAgICAgICAgbmV3Um93LnVzZXJuYW1lID0gaXRlbS51c2VybmFtZSB8fCAnJztcclxuICAgICAgICAgIG5ld1Jvdy5maXJzdF9uYW1lID0gaXRlbS51c2VyX2lkX2RhdGE/Lm5pY2tuYW1lIHx8IGl0ZW0udXNlcl9pZF9kYXRhPy5maXJzdF9uYW1lIHx8ICcnO1xyXG5cclxuICAgICAgICAgIC8vIFN0b3JlIHRoZSBzZWxlY3RlZCBkYXRhIGZvciBmb3JtIHN1Ym1pc3Npb24gKGZvbGxvd2luZyBHZW5lcmFsVGFiIHBhdHRlcm4pXHJcbiAgICAgICAgICBpZiAoaXRlbS5tYV9jdCkge1xyXG4gICAgICAgICAgICBzZXRTZWxlY3RlZENodW5nVHVEYXRhKHByZXYgPT4gKHtcclxuICAgICAgICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgICAgICAgIFtuZXdSb3cudXVpZF06IGl0ZW1cclxuICAgICAgICAgICAgfSkpO1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGlmIChpdGVtLnVzZXJuYW1lICYmIGl0ZW0udXNlcl9pZF9kYXRhKSB7XHJcbiAgICAgICAgICAgIHNldFNlbGVjdGVkVXNlckRhdGEocHJldiA9PiAoe1xyXG4gICAgICAgICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgICAgICAgW25ld1Jvdy51dWlkXTogaXRlbVxyXG4gICAgICAgICAgICB9KSk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgcmV0dXJuIG5ld1JvdztcclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgLy8gU2V0IHRoZSB0YWJsZSBkYXRhXHJcbiAgICAgICAgaWYgKHRhYmxlUm93cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAvLyBVc2UgdGhlIHNldFJvd3MgZnVuY3Rpb24gZnJvbSB1c2VJbnB1dFRhYmxlUm93XHJcbiAgICAgICAgICBzZXRSb3dzKHRhYmxlUm93cyk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBQcm9jZXNzIGluaXRpYWxEYXRhLmRhbmhfc2FjaF9zb19oaWVuX3RhaSBpZiBhdmFpbGFibGVcclxuICAgICAgaWYgKFxyXG4gICAgICAgIGluaXRpYWxEYXRhLmRhbmhfc2FjaF9zb19oaWVuX3RhaSAmJlxyXG4gICAgICAgIEFycmF5LmlzQXJyYXkoaW5pdGlhbERhdGEuZGFuaF9zYWNoX3NvX2hpZW5fdGFpKSAmJlxyXG4gICAgICAgIGluaXRpYWxEYXRhLmRhbmhfc2FjaF9zb19oaWVuX3RhaS5sZW5ndGggPiAwXHJcbiAgICAgICkge1xyXG4gICAgICAgIC8vIENvbnZlcnQgZGFuaF9zYWNoX3NvX2hpZW5fdGFpIGFycmF5IHRvIHRoZSBmb3JtYXQgZXhwZWN0ZWQgYnkgdGhlIHRhYmxlXHJcbiAgICAgICAgY29uc3Qgc29IaWVuVGFpVGFibGVSb3dzID0gaW5pdGlhbERhdGEuZGFuaF9zYWNoX3NvX2hpZW5fdGFpLm1hcCgoaXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAvLyBDcmVhdGUgYSBuZXcgcm93IHdpdGggYSB1bmlxdWUgVVVJRFxyXG4gICAgICAgICAgY29uc3QgbmV3Um93ID0gY3JlYXRlU29IaWVuVGFpUm93KCk7XHJcblxyXG4gICAgICAgICAgLy8gU2V0IHRoZSB2YWx1ZXMgZnJvbSB0aGUgZGFuaF9zYWNoX3NvX2hpZW5fdGFpIGl0ZW1cclxuICAgICAgICAgIG5ld1Jvdy5uZ2F5ID0gaXRlbS5uZ2F5IHx8ICcnO1xyXG4gICAgICAgICAgbmV3Um93LnNvX2hpZW5fdGFpID0gaXRlbS5zb19oaWVuX3RhaSB8fCAnJztcclxuXHJcbiAgICAgICAgICByZXR1cm4gbmV3Um93O1xyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBTZXQgdGhlIHRhYmxlIGRhdGEgZm9yIFwiU+G7kSBoaeG7h24gdOG6oWlcIiB0YWJcclxuICAgICAgICBpZiAoc29IaWVuVGFpVGFibGVSb3dzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgIHNldFNvSGllblRhaVJvd3Moc29IaWVuVGFpVGFibGVSb3dzKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbaW5pdGlhbERhdGEsIHNldFJvd3MsIHNldFNvSGllblRhaVJvd3MsIGNyZWF0ZUdpYUJhblJvdywgY3JlYXRlU29IaWVuVGFpUm93XSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8QXJpdG9EaWFsb2dcclxuICAgICAgICBvcGVuPXtvcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9e29uQ2xvc2V9XHJcbiAgICAgICAgdGl0bGU9e21vZGUgPT09ICdhZGQnID8gJ03hu5tpJyA6IG1vZGUgPT09ICdlZGl0JyA/ICdT4butYScgOiAnWGVtJ31cclxuICAgICAgICBtYXhXaWR0aD0nbGcnXHJcbiAgICAgICAgZGlzYWJsZUJhY2tkcm9wQ2xvc2U9e2ZhbHNlfVxyXG4gICAgICAgIGRpc2FibGVFc2NhcGVLZXlEb3duPXtmYWxzZX1cclxuICAgICAgICB0aXRsZUljb249ezxBcml0b0ljb24gaWNvbj17MjgxfSAvPn1cclxuICAgICAgPlxyXG4gICAgICAgIDxBcml0b0Zvcm1cclxuICAgICAgICAgIG1vZGU9e21vZGV9XHJcbiAgICAgICAgICBoYXNBcml0b0FjdGlvbkJhcj17ZmFsc2V9XHJcbiAgICAgICAgICBzY2hlbWE9e2Zvcm1TY2hlbWF9XHJcbiAgICAgICAgICBvblN1Ym1pdD17ZGF0YSA9PiB7XHJcbiAgICAgICAgICAgIGhhbmRsZVN1Ym1pdChkYXRhKTtcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgICBpbml0aWFsRGF0YT17aW5pdGlhbERhdGF9XHJcbiAgICAgICAgICBjbGFzc05hbWU9J3ctWzUwdnddJ1xyXG4gICAgICAgICAgaGVhZGVyRmllbGRzPXs8QmFzaWNGb3JtIG1vZGU9e21vZGV9IC8+fVxyXG4gICAgICAgICAgY2xhc3NOYW1lQm90dG9tQmFyPSdyZWxhdGl2ZSB3LWZ1bGwgZmxleCBqdXN0aWZ5LWVuZCBnYXAtMidcclxuICAgICAgICAgIHRhYnM9e1tcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIGlkOiAnMScsXHJcbiAgICAgICAgICAgICAgbGFiZWw6ICdUaMO0bmcgdGluIGNodW5nJyxcclxuICAgICAgICAgICAgICBjb21wb25lbnQ6IDxHZW5lcmFsVGFiIG1vZGU9e21vZGV9IGRvblZpPXtkb25WaX0gc2V0RG9uVmk9e3NldERvblZpfSAvPlxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgaWQ6ICczJyxcclxuICAgICAgICAgICAgICBsYWJlbDogJ0No4bupbmcgdOG7qyAmIE5nxrDhu51pIHPhu60gZOG7pW5nJyxcclxuICAgICAgICAgICAgICBjb21wb25lbnQ6IChcclxuICAgICAgICAgICAgICAgIDxJbnB1dFRhYmxlVGFiXHJcbiAgICAgICAgICAgICAgICAgIG1vZGU9e21vZGV9XHJcbiAgICAgICAgICAgICAgICAgIHJvd3M9e3RhYmxlRGF0YX1cclxuICAgICAgICAgICAgICAgICAgb25Sb3dDbGljaz17aGFuZGxlUm93Q2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgIG9uQWRkUm93PXtoYW5kbGVBZGRSb3d9XHJcbiAgICAgICAgICAgICAgICAgIG9uRGVsZXRlUm93PXtoYW5kbGVEZWxldGVSb3d9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ29weVJvdz17aGFuZGxlQ29weVJvd31cclxuICAgICAgICAgICAgICAgICAgb25QYXN0ZVJvdz17aGFuZGxlUGFzdGVSb3d9XHJcbiAgICAgICAgICAgICAgICAgIG9uTW92ZVJvdz17aGFuZGxlTW92ZVJvd31cclxuICAgICAgICAgICAgICAgICAgb25DZWxsVmFsdWVDaGFuZ2U9e2hhbmRsZUNlbGxWYWx1ZUNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgb25DaHVuZ1R1U2VsZWN0PXtoYW5kbGVDaHVuZ1R1U2VsZWN0fVxyXG4gICAgICAgICAgICAgICAgICBvblVzZXJTZWxlY3Q9e2hhbmRsZVVzZXJTZWxlY3R9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIGlkOiAnNCcsXHJcbiAgICAgICAgICAgICAgbGFiZWw6ICdT4buRIGhp4buHbiB04bqhaScsXHJcbiAgICAgICAgICAgICAgY29tcG9uZW50OiAoXHJcbiAgICAgICAgICAgICAgICA8U29IaWVuVGFpVGFiXHJcbiAgICAgICAgICAgICAgICAgIG1vZGU9e21vZGV9XHJcbiAgICAgICAgICAgICAgICAgIHJvd3M9e3NvSGllblRhaVRhYmxlRGF0YX1cclxuICAgICAgICAgICAgICAgICAgb25Sb3dDbGljaz17aGFuZGxlU29IaWVuVGFpUm93Q2xpY2t9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2VsbFZhbHVlQ2hhbmdlPXtoYW5kbGVTb0hpZW5UYWlDZWxsVmFsdWVDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgXX1cclxuICAgICAgICAgIGJvdHRvbUJhcj17XHJcbiAgICAgICAgICAgIDxCb3R0b21CYXJcclxuICAgICAgICAgICAgICBtb2RlPXttb2RlfVxyXG4gICAgICAgICAgICAgIG9uQWRkPXtvbkFkZEJ1dHRvbkNsaWNrfVxyXG4gICAgICAgICAgICAgIG9uRWRpdD17b25FZGl0QnV0dG9uQ2xpY2t9XHJcbiAgICAgICAgICAgICAgb25EZWxldGU9e29uRGVsZXRlQnV0dG9uQ2xpY2t9XHJcbiAgICAgICAgICAgICAgb25Db3B5PXtvbkNvcHlCdXR0b25DbGlja31cclxuICAgICAgICAgICAgICBvbkNsb3NlPXtvbkNsb3NlfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAge2Vycm9yICYmIDxkaXYgY2xhc3NOYW1lPSdteC00IG1iLTQgcm91bmRlZCBiZy1yZWQtMTAwIHAtMiB0ZXh0LXJlZC03MDAnPntlcnJvcn08L2Rpdj59XHJcbiAgICAgIDwvQXJpdG9EaWFsb2c+XHJcblxyXG4gICAgICA8Q29uZmlybURpYWxvZ1xyXG4gICAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsb3NlRGlhbG9nfVxyXG4gICAgICAgIG9wZW49e3Nob3dDb25maXJtRGlhbG9nfVxyXG4gICAgICAgIG9uQ2xvc2VDb25maXJtRGlhbG9nPXsoKSA9PiBzZXRTaG93Q29uZmlybURpYWxvZyhmYWxzZSl9XHJcbiAgICAgIC8+XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBGb3JtRGlhbG9nO1xyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsIkJvdHRvbUJhciIsIkFyaXRvRGlhbG9nIiwiQXJpdG9Gb3JtIiwidXNlSW5wdXRUYWJsZVJvdyIsIkFyaXRvSWNvbiIsIkNvbmZpcm1EaWFsb2ciLCJJbnB1dFRhYmxlVGFiIiwiZm9ybVNjaGVtYSIsIlNvSGllblRhaVRhYiIsIkdlbmVyYWxUYWIiLCJCYXNpY0Zvcm0iLCJGb3JtRGlhbG9nIiwibW9kZSIsIm9wZW4iLCJvbkNsb3NlIiwib25BZGRCdXR0b25DbGljayIsIm9uQ29weUJ1dHRvbkNsaWNrIiwib25EZWxldGVCdXR0b25DbGljayIsIm9uRWRpdEJ1dHRvbkNsaWNrIiwib25TdWJtaXQiLCJpbml0aWFsRGF0YSIsInNob3dDb25maXJtRGlhbG9nIiwic2V0U2hvd0NvbmZpcm1EaWFsb2ciLCJlcnJvciIsInNldEVycm9yIiwiZG9uVmkiLCJzZXREb25WaSIsImNyZWF0ZUdpYUJhblJvdyIsIm5ld1JvdyIsInV1aWQiLCJTdHJpbmciLCJNYXRoIiwicmFuZG9tIiwibWFfY3QiLCJ0ZW5fY3QiLCJ1c2VyX2lkIiwidXNlcm5hbWUiLCJmaXJzdF9uYW1lIiwiY3JlYXRlU29IaWVuVGFpUm93IiwibmdheSIsInNvX2hpZW5fdGFpIiwicm93cyIsInRhYmxlRGF0YSIsInNldFJvd3MiLCJoYW5kbGVSb3dDbGljayIsImhhbmRsZUFkZFJvdyIsIm9yaWdpbmFsSGFuZGxlQWRkUm93IiwiaGFuZGxlRGVsZXRlUm93IiwiaGFuZGxlQ29weVJvdyIsImhhbmRsZVBhc3RlUm93IiwiaGFuZGxlTW92ZVJvdyIsImhhbmRsZUNlbGxWYWx1ZUNoYW5nZSIsIm9yaWdpbmFsSGFuZGxlQ2VsbFZhbHVlQ2hhbmdlIiwic29IaWVuVGFpVGFibGVEYXRhIiwic2V0U29IaWVuVGFpUm93cyIsImhhbmRsZVNvSGllblRhaVJvd0NsaWNrIiwib3JpZ2luYWxIYW5kbGVTb0hpZW5UYWlDZWxsVmFsdWVDaGFuZ2UiLCJyb3dVdWlkIiwiZmllbGQiLCJuZXdWYWx1ZSIsImhhbmRsZVNvSGllblRhaUNlbGxWYWx1ZUNoYW5nZSIsInNlbGVjdGVkQ2h1bmdUdURhdGEiLCJzZXRTZWxlY3RlZENodW5nVHVEYXRhIiwic2VsZWN0ZWRVc2VyRGF0YSIsInNldFNlbGVjdGVkVXNlckRhdGEiLCJoYW5kbGVDaHVuZ1R1U2VsZWN0IiwiY2h1bmdUdSIsInByZXYiLCJ1cGRhdGVkIiwiaGFuZGxlVXNlclNlbGVjdCIsInVzZXIiLCJoYW5kbGVTdWJtaXQiLCJkYXRhIiwiY29uc29sZSIsImxvZyIsImNvbWJpbmVkTGlzdCIsIm1hcCIsInJvdyIsImluZGV4IiwibGluZSIsInByb2ZpbGUiLCJzb0hpZW5UYWlDb21iaW5lZExpc3QiLCJmb3JtRGF0YSIsImRhbmhfc2FjaF9jaHVuZ190dSIsImRhbmhfc2FjaF9zb19oaWVuX3RhaSIsImVyciIsIm1lc3NhZ2UiLCJoYW5kbGVDbG9zZURpYWxvZyIsImNoaV90aWV0IiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwidGFibGVSb3dzIiwiaXRlbSIsInVzZXJfaWRfZGF0YSIsIm5pY2tuYW1lIiwic29IaWVuVGFpVGFibGVSb3dzIiwidGl0bGUiLCJtYXhXaWR0aCIsImRpc2FibGVCYWNrZHJvcENsb3NlIiwiZGlzYWJsZUVzY2FwZUtleURvd24iLCJ0aXRsZUljb24iLCJpY29uIiwiaGFzQXJpdG9BY3Rpb25CYXIiLCJzY2hlbWEiLCJjbGFzc05hbWUiLCJoZWFkZXJGaWVsZHMiLCJjbGFzc05hbWVCb3R0b21CYXIiLCJ0YWJzIiwiaWQiLCJsYWJlbCIsImNvbXBvbmVudCIsIm9uUm93Q2xpY2siLCJvbkFkZFJvdyIsIm9uRGVsZXRlUm93Iiwib25Db3B5Um93Iiwib25QYXN0ZVJvdyIsIm9uTW92ZVJvdyIsIm9uQ2VsbFZhbHVlQ2hhbmdlIiwib25DaHVuZ1R1U2VsZWN0Iiwib25Vc2VyU2VsZWN0IiwiYm90dG9tQmFyIiwib25BZGQiLCJvbkVkaXQiLCJvbkRlbGV0ZSIsIm9uQ29weSIsImRpdiIsIm9uQ2xvc2VDb25maXJtRGlhbG9nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx\n"));

/***/ })

});