import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import InputTableActionBar from './InputTableActionBar';
import { getInputTableColumns } from './cols';

import { GridCellParams } from '@mui/x-data-grid';
import { SelectedCellInfo } from './useInputTableRow';
import type { ChungTu } from '@/types/schemas';
import type { FormMode } from '@/types/form';

// Interface for User data (matching backend structure with profile)
interface UserProfile {
  uuid: string;
  nickname?: string | null;
}

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name?: string;
  is_staff?: boolean;
  is_active?: boolean;
  date_joined?: string;
  profile: UserProfile;
}

interface InputTableTabProps {
  mode: FormMode;
  rows: any[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;

  onChungTuSelect?: (rowUuid: string, chungTu: ChungTu) => void;
  onUserSelect?: (rowUuid: string, user: User) => void;
}

const InputTableTab: React.FC<InputTableTabProps> = ({
  mode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onCellValueChange,
  onChungTuSelect,
  onUserSelect
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getInputTableColumns({
        onCellValueChange,
        onChungTuSelect,
        onUserSelect
      })}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <InputTableActionBar
          mode={mode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
        />
      }
    />
  );
};

export default InputTableTab;
