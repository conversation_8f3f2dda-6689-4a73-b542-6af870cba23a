"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx":
/*!***********************************************************************************************!*\
  !*** ./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx ***!
  \***********************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/custom/arito/form/bottom-bar */ \"(app-pages-browser)/./src/components/custom/arito/form/bottom-bar.tsx\");\n/* harmony import */ var _components_custom_arito__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/custom/arito */ \"(app-pages-browser)/./src/components/custom/arito/index.ts\");\n/* harmony import */ var _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./InputTableTab/useInputTableRow */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/useInputTableRow.ts\");\n/* harmony import */ var _components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/custom/arito/icon */ \"(app-pages-browser)/./src/components/custom/arito/icon/index.tsx\");\n/* harmony import */ var _ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ConfirmDialog */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/ConfirmDialog.tsx\");\n/* harmony import */ var _InputTableTab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./InputTableTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/InputTableTab/index.tsx\");\n/* harmony import */ var _schemas__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../schemas */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/schemas.ts\");\n/* harmony import */ var _SoHienTaiTab__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./SoHienTaiTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/SoHienTaiTab/index.tsx\");\n/* harmony import */ var _GeneralTab__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./GeneralTab */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/GeneralTab.tsx\");\n/* harmony import */ var _BasicForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./BasicForm */ \"(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/BasicForm.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FormDialog(param) {\n    let { mode, open, onClose, onAddButtonClick, onCopyButtonClick, onDeleteButtonClick, onEditButtonClick, onSubmit, initialData } = param;\n    _s();\n    const [showConfirmDialog, setShowConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [donVi, setDonVi] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Chứng từ & Người sử dụng\"\n    const createGiaBanRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ma_ct: \"\",\n            ten_ct: \"\",\n            user_id: \"\",\n            username: \"\",\n            first_name: \"\" // Tên người sử dụng để hiển thị\n        };\n        return newRow;\n    }, []);\n    // Hàm tạo dòng mới tùy chỉnh cho bảng \"Số hiện tại\"\n    const createSoHienTaiRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newRow = {\n            uuid: String(Math.random()),\n            ngay: \"\",\n            so_hien_tai: \"\" // Số hiện tại\n        };\n        return newRow;\n    }, []);\n    // Sử dụng hook useInputTableRow cho tab \"Chứng từ & Người sử dụng\"\n    const { rows: tableData, setRows, handleRowClick, handleAddRow: originalHandleAddRow, handleDeleteRow, handleCopyRow, handlePasteRow, handleMoveRow, handleCellValueChange: originalHandleCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createGiaBanRow);\n    const { rows: soHienTaiTableData, setRows: setSoHienTaiRows, handleRowClick: handleSoHienTaiRowClick, handleCellValueChange: originalHandleSoHienTaiCellValueChange } = (0,_InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([], createSoHienTaiRow);\n    // Wrap handleAddRow to add logging\n    const handleAddRow = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        originalHandleAddRow();\n    }, [\n        originalHandleAddRow\n    ]);\n    // Wrap handleCellValueChange to add logging\n    const handleCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleCellValueChange\n    ]);\n    // Wrap handlers for \"Số hiện tại\" tab\n    const handleSoHienTaiCellValueChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, field, newValue)=>{\n        originalHandleSoHienTaiCellValueChange(rowUuid, field, newValue);\n    }, [\n        originalHandleSoHienTaiCellValueChange\n    ]);\n    // State for storing selected data following GeneralTab pattern\n    const [selectedChungTuData, setSelectedChungTuData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedUserData, setSelectedUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Callback handlers following GeneralTab pattern\n    const handleChungTuSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, chungTu)=>{\n        setSelectedChungTuData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: chungTu\n            };\n            return updated;\n        });\n    }, []);\n    const handleUserSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((rowUuid, user)=>{\n        setSelectedUserData((prev)=>{\n            const updated = {\n                ...prev,\n                [rowUuid]: user\n            };\n            return updated;\n        });\n    }, []);\n    const handleSubmit = async (data)=>{\n        console.log(\"Form data>>>>>>>>>>>:\", data);\n        console.log(\"Table data>>>>>>>>>>>:\", tableData);\n        // Create combined list with ma_ct and username format for \"Chứng từ & Người sử dụng\" tab\n        // Map to chi_tiet format for API submission\n        const combinedList = tableData.map((row, index)=>{\n            var _user_profile;\n            const chungTu = selectedChungTuData[row.uuid];\n            const user = selectedUserData[row.uuid];\n            return {\n                line: index + 1,\n                ma_ct: (chungTu === null || chungTu === void 0 ? void 0 : chungTu.uuid) || \"\",\n                username: (user === null || user === void 0 ? void 0 : user.username) || row.username || \"\",\n                user_id: (user === null || user === void 0 ? void 0 : (_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.uuid) || null // Use profile UUID instead of integer ID\n            };\n        });\n        // Create combined list for \"Số hiện tại\" tab\n        const soHienTaiCombinedList = soHienTaiTableData.map((row)=>{\n            return {\n                ngay: row.ngay || \"\",\n                so_hien_tai: row.so_hien_tai || \"\"\n            };\n        });\n        // Log the combined lists in requested format\n        console.log(\"\\uD83D\\uDCCB\\uD83D\\uDC65 List of ChungTu and User:\", combinedList);\n        try {\n            if (onSubmit) {\n                const formData = {\n                    ...data,\n                    danh_sach_chung_tu: combinedList,\n                    danh_sach_so_hien_tai: soHienTaiCombinedList\n                };\n                await onSubmit(formData);\n            } else {\n                setError(\"Kh\\xf4ng thể lưu dữ liệu: Kh\\xf4ng c\\xf3 xử l\\xfd submit\");\n            }\n        } catch (err) {\n            setError(err.message || \"C\\xf3 lỗi xảy ra\");\n        }\n    };\n    const handleCloseDialog = ()=>{\n        setShowConfirmDialog(false);\n        onClose();\n    };\n    // Effect to update form fields when initialData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            // Process initialData.chi_tiet if available (map from API response)\n            if (initialData.chi_tiet && Array.isArray(initialData.chi_tiet) && initialData.chi_tiet.length > 0) {\n                // Convert chi_tiet array to the format expected by the table\n                const tableRows = initialData.chi_tiet.map((item)=>{\n                    var _item_user_id_data, _item_user_id_data1;\n                    // Create a new row with a unique UUID\n                    const newRow = createGiaBanRow();\n                    newRow.ma_ct = item.ma_ct || \"\";\n                    newRow.ten_ct = item.ten_ct || \"\";\n                    newRow.user_id = item.user_id || \"\"; // This will now be UUID from profile\n                    newRow.username = item.username || \"\";\n                    newRow.first_name = ((_item_user_id_data = item.user_id_data) === null || _item_user_id_data === void 0 ? void 0 : _item_user_id_data.nickname) || ((_item_user_id_data1 = item.user_id_data) === null || _item_user_id_data1 === void 0 ? void 0 : _item_user_id_data1.first_name) || \"\";\n                    // Store the selected data for form submission (following GeneralTab pattern)\n                    if (item.ma_ct) {\n                        setSelectedChungTuData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: item\n                            }));\n                    }\n                    if (item.username && item.user_id_data) {\n                        var _item_user_id_data2, _item_user_id_data3, _item_user_id_data4, _item_user_id_data5, _item_user_id_data6, _item_user_id_data7, _item_user_id_data8, _item_user_id_data9;\n                        // Create a User object that matches the new interface structure\n                        const userData = {\n                            id: ((_item_user_id_data2 = item.user_id_data) === null || _item_user_id_data2 === void 0 ? void 0 : _item_user_id_data2.id) || 0,\n                            username: item.username,\n                            email: ((_item_user_id_data3 = item.user_id_data) === null || _item_user_id_data3 === void 0 ? void 0 : _item_user_id_data3.email) || \"\",\n                            first_name: ((_item_user_id_data4 = item.user_id_data) === null || _item_user_id_data4 === void 0 ? void 0 : _item_user_id_data4.first_name) || \"\",\n                            last_name: ((_item_user_id_data5 = item.user_id_data) === null || _item_user_id_data5 === void 0 ? void 0 : _item_user_id_data5.last_name) || \"\",\n                            is_staff: ((_item_user_id_data6 = item.user_id_data) === null || _item_user_id_data6 === void 0 ? void 0 : _item_user_id_data6.is_staff) || false,\n                            is_active: ((_item_user_id_data7 = item.user_id_data) === null || _item_user_id_data7 === void 0 ? void 0 : _item_user_id_data7.is_active) || true,\n                            date_joined: ((_item_user_id_data8 = item.user_id_data) === null || _item_user_id_data8 === void 0 ? void 0 : _item_user_id_data8.date_joined) || \"\",\n                            profile: {\n                                uuid: item.user_id || \"\",\n                                nickname: ((_item_user_id_data9 = item.user_id_data) === null || _item_user_id_data9 === void 0 ? void 0 : _item_user_id_data9.nickname) || null\n                            }\n                        };\n                        setSelectedUserData((prev)=>({\n                                ...prev,\n                                [newRow.uuid]: userData\n                            }));\n                    }\n                    return newRow;\n                });\n                // Set the table data\n                if (tableRows.length > 0) {\n                    // Use the setRows function from useInputTableRow\n                    setRows(tableRows);\n                }\n            }\n            // Process initialData.danh_sach_so_hien_tai if available\n            if (initialData.danh_sach_so_hien_tai && Array.isArray(initialData.danh_sach_so_hien_tai) && initialData.danh_sach_so_hien_tai.length > 0) {\n                // Convert danh_sach_so_hien_tai array to the format expected by the table\n                const soHienTaiTableRows = initialData.danh_sach_so_hien_tai.map((item)=>{\n                    // Create a new row with a unique UUID\n                    const newRow = createSoHienTaiRow();\n                    // Set the values from the danh_sach_so_hien_tai item\n                    newRow.ngay = item.ngay || \"\";\n                    newRow.so_hien_tai = item.so_hien_tai || \"\";\n                    return newRow;\n                });\n                // Set the table data for \"Số hiện tại\" tab\n                if (soHienTaiTableRows.length > 0) {\n                    setSoHienTaiRows(soHienTaiTableRows);\n                }\n            }\n        }\n    }, [\n        initialData,\n        setRows,\n        setSoHienTaiRows,\n        createGiaBanRow,\n        createSoHienTaiRow\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoDialog, {\n                open: open,\n                onClose: onClose,\n                title: mode === \"add\" ? \"Mới\" : mode === \"edit\" ? \"Sửa\" : \"Xem\",\n                maxWidth: \"lg\",\n                disableBackdropClose: false,\n                disableEscapeKeyDown: false,\n                titleIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_icon__WEBPACK_IMPORTED_MODULE_5__.AritoIcon, {\n                    icon: 281\n                }, void 0, false, void 0, void 0),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito__WEBPACK_IMPORTED_MODULE_3__.AritoForm, {\n                        mode: mode,\n                        hasAritoActionBar: false,\n                        schema: _schemas__WEBPACK_IMPORTED_MODULE_8__.formSchema,\n                        onSubmit: (data)=>{\n                            handleSubmit(data);\n                        },\n                        initialData: initialData,\n                        className: \"w-[50vw]\",\n                        headerFields: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BasicForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            mode: mode\n                        }, void 0, false, void 0, void 0),\n                        classNameBottomBar: \"relative w-full flex justify-end gap-2\",\n                        tabs: [\n                            {\n                                id: \"1\",\n                                label: \"Th\\xf4ng tin chung\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GeneralTab__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    mode: mode,\n                                    donVi: donVi,\n                                    setDonVi: setDonVi\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"3\",\n                                label: \"Chứng từ & Người sử dụng\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputTableTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    mode: mode,\n                                    rows: tableData,\n                                    onRowClick: handleRowClick,\n                                    onAddRow: handleAddRow,\n                                    onDeleteRow: handleDeleteRow,\n                                    onCopyRow: handleCopyRow,\n                                    onPasteRow: handlePasteRow,\n                                    onMoveRow: handleMoveRow,\n                                    onCellValueChange: handleCellValueChange,\n                                    onChungTuSelect: handleChungTuSelect,\n                                    onUserSelect: handleUserSelect\n                                }, void 0, false, void 0, void 0)\n                            },\n                            {\n                                id: \"4\",\n                                label: \"Số hiện tại\",\n                                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SoHienTaiTab__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    mode: mode,\n                                    rows: soHienTaiTableData,\n                                    onRowClick: handleSoHienTaiRowClick,\n                                    onCellValueChange: handleSoHienTaiCellValueChange\n                                }, void 0, false, void 0, void 0)\n                            }\n                        ],\n                        bottomBar: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_arito_form_bottom_bar__WEBPACK_IMPORTED_MODULE_2__.BottomBar, {\n                            mode: mode,\n                            onAdd: onAddButtonClick,\n                            onEdit: onEditButtonClick,\n                            onDelete: onDeleteButtonClick,\n                            onCopy: onCopyButtonClick,\n                            onClose: onClose\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-4 mb-4 rounded bg-red-100 p-2 text-red-700\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 19\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ConfirmDialog__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                onClose: handleCloseDialog,\n                open: showConfirmDialog,\n                onCloseConfirmDialog: ()=>setShowConfirmDialog(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\StudyWork\\\\TTMI\\\\ERP\\\\erp-fe\\\\src\\\\features\\\\he-thong\\\\chung-tu\\\\cau-truc-quyen-so-chung-tu\\\\components\\\\dialog\\\\index.tsx\",\n                lineNumber: 363,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FormDialog, \"AE6QFM1+LCL/ZsIU5CFTwcEJQvY=\", false, function() {\n    return [\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _InputTableTab_useInputTableRow__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = FormDialog;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FormDialog);\nvar _c;\n$RefreshReg$(_c, \"FormDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/components/dialog/index.tsx\n"));

/***/ })

});